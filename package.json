{"name": "axiia-pea-web", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "dependencies": {"@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.1.2", "@remix-run/node": "^2.15.3", "@remix-run/react": "^2.15.3", "@remix-run/serve": "^2.15.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.4.3", "isbot": "^4.1.0", "libphonenumber-js": "^1.10.37", "lucide-react": "^0.475.0", "nanoid": "^4.0.2", "nodemailer": "^6.10.1", "pg-hstore": "^2.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "sequelize": "^6.37.1", "sqlite3": "^5.1.7", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.5", "zod": "^3.21.4"}, "devDependencies": {"@remix-run/dev": "^2.15.3", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "eslint": "^8.38.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "prettier": "^3.5.3", "sequelize-cli": "^6.6.2", "tailwindcss": "^3.4.4", "typescript": "^5.1.6", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}