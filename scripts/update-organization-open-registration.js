import { Sequelize } from 'sequelize';

// Initialize Sequelize with the same configuration as the app
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: process.env.DATABASE_URL || './data/sqlite.db',
  logging: console.log,
});

async function updateOrganization() {
  try {
    console.log('Updating organization to open registration...');

    // Test connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Get command line argument for organization ID
    const orgId = process.argv[2];
    
    if (!orgId) {
      console.log('Usage: node scripts/update-organization-open-registration.js <organization-id>');
      console.log('\nAvailable organizations:');
      
      const [results] = await sequelize.query('SELECT id, name, is_open_registration FROM organizations');
      results.forEach((org, index) => {
        console.log(`${index + 1}. ${org.id} - ${org.name} (Open: ${org.is_open_registration ? 'Yes' : 'No'})`);
      });
      return;
    }

    // Update the organization
    const [affectedRows] = await sequelize.query(
      'UPDATE organizations SET is_open_registration = 1 WHERE id = ?',
      { replacements: [orgId] }
    );

    if (affectedRows === 0) {
      console.log('No organization found with that ID.');
    } else {
      console.log(`✓ Successfully updated organization ${orgId} to open registration.`);
      console.log(`\nYou can now test with URL: /login?id=${orgId}`);
    }

  } catch (error) {
    console.error('Error updating organization:', error);
  } finally {
    await sequelize.close();
  }
}

updateOrganization();
