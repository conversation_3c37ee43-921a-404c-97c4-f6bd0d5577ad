import { Sequelize, DataTypes } from 'sequelize';
import path from 'path';

// Initialize Sequelize with the same configuration as the app
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: process.env.DATABASE_URL || './data/sqlite.db',
  logging: console.log,
});

async function migrate() {
  try {
    console.log('Starting migration for open registration feature...');

    // Test connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Get query interface
    const queryInterface = sequelize.getQueryInterface();

    // 1. Add is_open_registration column to organizations table
    console.log('Adding is_open_registration column to organizations table...');
    try {
      await queryInterface.addColumn('organizations', 'is_open_registration', {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      });
      console.log('✓ Added is_open_registration column');
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log('✓ is_open_registration column already exists');
      } else {
        throw error;
      }
    }

    // 2. Create organization_users table
    console.log('Creating organization_users table...');
    try {
      await queryInterface.createTable('organization_users', {
        id: {
          type: DataTypes.STRING,
          primaryKey: true,
          allowNull: false,
        },
        organization_id: {
          type: DataTypes.STRING,
          allowNull: false,
          references: {
            model: 'organizations',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        user_id: {
          type: DataTypes.STRING,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        created_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
        },
      });

      // Add unique constraint
      await queryInterface.addIndex('organization_users', {
        fields: ['organization_id', 'user_id'],
        unique: true,
        name: 'organization_users_unique_constraint',
      });

      console.log('✓ Created organization_users table');
    } catch (error) {
      if (error.message.includes('table organization_users already exists')) {
        console.log('✓ organization_users table already exists');
      } else {
        throw error;
      }
    }

    console.log('Migration completed successfully!');
    
    // Verify the changes
    console.log('\nVerifying migration...');
    
    // Check organizations table structure
    const orgTableInfo = await queryInterface.describeTable('organizations');
    if (orgTableInfo.is_open_registration) {
      console.log('✓ organizations.is_open_registration column exists');
    } else {
      console.log('✗ organizations.is_open_registration column missing');
    }

    // Check organization_users table structure
    try {
      const orgUsersTableInfo = await queryInterface.describeTable('organization_users');
      console.log('✓ organization_users table exists with columns:', Object.keys(orgUsersTableInfo));
    } catch (error) {
      console.log('✗ organization_users table missing');
    }

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run migration if this script is executed directly
migrate();
