import { Sequelize, DataTypes } from 'sequelize';

// Initialize Sequelize with the same configuration as the app
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: process.env.DATABASE_URL || './data/sqlite.db',
  logging: console.log,
});

async function checkOrganizations() {
  try {
    console.log('Checking organizations in database...');

    // Test connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Query organizations
    const [results] = await sequelize.query('SELECT * FROM organizations');
    
    console.log('\nOrganizations found:');
    console.log('===================');
    
    if (results.length === 0) {
      console.log('No organizations found in database.');
    } else {
      results.forEach((org, index) => {
        console.log(`${index + 1}. ID: ${org.id}`);
        console.log(`   Name: ${org.name}`);
        console.log(`   Problem Set ID: ${org.problem_set_id}`);
        console.log(`   Open Registration: ${org.is_open_registration ? 'Yes' : 'No'}`);
        console.log(`   Created: ${org.created_at}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('Error checking organizations:', error);
  } finally {
    await sequelize.close();
  }
}

checkOrganizations();
