# Open Registration Organization Implementation

## Overview
This implementation adds support for open registration organizations, allowing users to register and create tests without being in the whitelist.

## Features Implemented

### 1. Database Schema Changes
- **Organization Model**: Added `isOpenRegistration` boolean field to distinguish between whitelist-only and open registration organizations
- **OrganizationUser Model**: New model to track which users have registered for which organizations
- **Database Associations**: Added proper relationships between Organization, User, and OrganizationUser models

### 2. Organization Management
- **Create Organization**: Admin can now mark organizations as "Open Registration" when creating them
- **Organization List**: Shows registration type (Open Registration vs Whitelist Only) with color-coded badges
- **Member Management**: New admin page to view organization members and their associated tests

### 3. Login Flow Updates
- **Whitelist Bypass**: For open registration organizations, users can register without being in the whitelist
- **Automatic Registration**: Users are automatically added to the organization when they complete verification
- **Test Creation**: Same test creation flow applies for both registration types

### 4. Admin Interface
- **Organization Creation Form**: Added checkbox to enable open registration
- **Organization Table**: Added "Registration Type" column with visual indicators
- **Member View**: New page to view all members of an organization and their tests
- **URL Display**: Shows the registration URL for open registration organizations

## File Changes

### Models
- `app/models/organization.model.ts` - Added `isOpenRegistration` field
- `app/models/organization-user.model.ts` - New model for tracking organization membership
- `app/models/index.ts` - Added new model and associations
- `app/lib/types.ts` - Updated Organization interface

### Server Functions
- `app/lib/organization.server.ts` - Added functions for open registration logic and member management

### Routes
- `app/routes/login.tsx` - Updated to support open registration flow
- `app/routes/pea-admin.organizations.tsx` - Enhanced with open registration options and member links
- `app/routes/pea-admin.organization-members.$orgId.tsx` - New page for viewing organization members

### Components
- `app/components/ui/checkbox.tsx` - New checkbox component for the admin interface

## Usage

### For Administrators
1. **Create Open Registration Organization**:
   - Go to Admin > Organizations
   - Click "Add Organization"
   - Check "Open Registration" checkbox
   - Fill in other details and create

2. **View Organization Members**:
   - Go to Admin > Organizations
   - Click the "Users" icon next to any organization
   - View all registered members and their tests

### For Users
1. **Register with Open Registration Organization**:
   - Use URL: `/login?id={organizationId}`
   - Enter email address (no whitelist check for open registration orgs)
   - Receive and enter verification code
   - Automatically registered and test created

### URL Format
- Open registration organizations: `/login?id={organizationId}`
- Same URL format as before, but no whitelist requirement

## Database Migration
The implementation includes new fields and tables:
- `organizations.is_open_registration` (boolean, default false)
- `organization_users` table with organization_id, user_id, and created_at

## Benefits
1. **Flexibility**: Organizations can choose between controlled (whitelist) and open registration
2. **Scalability**: Open registration allows for larger user bases without manual whitelist management
3. **Tracking**: Full visibility into organization membership and test activity
4. **Backward Compatibility**: Existing whitelist-only organizations continue to work unchanged

## Security Considerations
- Open registration organizations still require email verification
- Admin access is still required to create organizations
- Test creation follows the same security model
- Organization membership is tracked for audit purposes
