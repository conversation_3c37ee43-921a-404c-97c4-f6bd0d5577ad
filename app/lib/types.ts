export interface ProblemSet {
  id: string
  problemIds: string[]
  createdAt?: Date
}

export interface Test {
  id: string
  problemSetId: string
  userId: string
  url: string
  createdAt?: Date
}

export interface User {
  id: string
  name: string
  email?: string
  phoneNumber?: string
  createdAt?: Date
}

export interface Organization {
  id: string
  name: string
  problemSetId: string
  createdAt?: Date
  updatedAt?: Date
}

export type ContactInfo = {
  email?: string
  phoneNumber?: string
}

export type UserRegistration = {
  name: string
} & (
  | { email: string; phoneNumber?: string }
  | { email?: string; phoneNumber: string }
  | { email: string; phoneNumber: string }
)
