import { nanoid } from 'nanoid';
import { validateEmail, normalizeEmail } from './utils';
import { Whitelist, VerificationCode } from '~/models';
import { Op } from 'sequelize';
import nodemailer from 'nodemailer';

export class EmailVerificationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'EmailVerificationError';
  }
}

export async function generateVerificationCode(email: string): Promise<string> {
  if (!validateEmail(email)) {
    throw new EmailVerificationError('Invalid email format');
  }
  
  const normalizedEmail = normalizeEmail(email);
  
  const isWhitelisted = await isEmailInWhitelist(normalizedEmail);
  if (!isWhitelisted) {
    throw new EmailVerificationError('Email not in whitelist');
  }
  
  try {
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
    
    await VerificationCode.destroy({
      where: { email: normalizedEmail }
    });
    
    await VerificationCode.create({
      id: nanoid(),
      email: normalizedEmail,
      code,
      expiresAt
    });
    
    return code;
  } catch (error) {
    console.error('Error generating verification code:', error);
    throw new EmailVerificationError('Failed to generate verification code');
  }
}

export async function verifyCode(email: string, code: string): Promise<boolean> {
  if (!validateEmail(email)) {
    throw new EmailVerificationError('Invalid email format');
  }
  
  const normalizedEmail = normalizeEmail(email);
  
  try {
    const verificationRecord = await VerificationCode.findOne({
      where: { email: normalizedEmail }
    });
    
    if (!verificationRecord) {
      return false;
    }
    
    if (new Date() > verificationRecord.expiresAt) {
      await verificationRecord.destroy();
      return false;
    }
    
    const storedCode = verificationRecord.getDataValue('code');
    const isValid = storedCode === code;
    
    if (isValid) {
      await verificationRecord.destroy();
    }
    
    return isValid;
  } catch (error) {
    console.error('Error verifying code:', error);
    return false;
  }
}

export async function sendVerificationEmail(email: string, code: string): Promise<boolean> {
  const SMTP_HOST = process.env.SMTP_HOST || '';
  const SMTP_PORT = parseInt(process.env.SMTP_PORT || '465', 10);
  const SMTP_USER = process.env.SMTP_USER || '';
  const SMTP_PASS = process.env.SMTP_PASS || '';
  const SMTP_FROM = process.env.SMTP_FROM || '<EMAIL>';
  const SMTP_FROM_NAME = process.env.SMTP_FROM_NAME || 'Axiia PEA Platform';
  
  if (!SMTP_HOST || !SMTP_USER || !SMTP_PASS) {
    throw new EmailVerificationError('SMTP credentials not configured');
  }

  try {
    const transporter = nodemailer.createTransport({
      host: SMTP_HOST,
      port: SMTP_PORT,
      secure: SMTP_PORT === 465,
      auth: {
        user: SMTP_USER,
        pass: SMTP_PASS,
      },
    });

    const info = await transporter.sendMail({
      from: `"${SMTP_FROM_NAME}" <${SMTP_FROM}>`,
      to: email,
      subject: 'Your Verification Code',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Axiia AI 能力评估</h2>
          <p>您的验证码是: <strong>${code}</strong></p>
          <p>此验证码将在15分钟后过期。</p>
          <p>如果您未请求此验证码，请忽略此邮件。</p>
        </div>
      `
    });

    return !!info.messageId;
  } catch (error) {
    console.error('Failed to send verification email:', error);
    throw new EmailVerificationError('Failed to send verification email');
  }
}

export async function cleanupExpiredCodes(): Promise<number> {
  try {
    const result = await VerificationCode.destroy({
      where: {
        expiresAt: { [Op.lt]: new Date() }
      }
    });
    
    return result;
  } catch (error) {
    console.error('Error cleaning up expired codes:', error);
    return 0;
  }
}

export async function isEmailInWhitelist(email: string): Promise<boolean> {
  const normalizedEmail = normalizeEmail(email);
  try {
    const entry = await Whitelist.findOne({
      where: { email: normalizedEmail }
    });
    return !!entry;
  } catch (error) {
    console.error('Error checking whitelist:', error);
    return false;
  }
}

export async function addEmailToWhitelist(email: string): Promise<void> {
  if (!validateEmail(email)) {
    throw new EmailVerificationError('Invalid email format');
  }
  
  const normalizedEmail = normalizeEmail(email);
  
  try {
    const existing = await Whitelist.findOne({
      where: { email: normalizedEmail }
    });
    
    if (!existing) {
      await Whitelist.create({
        id: nanoid(),
        email: normalizedEmail
      });
    }
  } catch (error) {
    console.error('Error adding email to whitelist:', error);
    throw new EmailVerificationError('Failed to add email to whitelist');
  }
}

export async function importWhitelist(emails: string[]): Promise<{ added: string[], failed: string[] }> {
  const results = { added: [] as string[], failed: [] as string[] };
  
  for (const email of emails) {
    try {
      if (validateEmail(email)) {
        const normalizedEmail = normalizeEmail(email);
        
        const existing = await Whitelist.findOne({
          where: { email: normalizedEmail }
        });
        
        if (!existing) {
          await Whitelist.create({
            id: nanoid(),
            email: normalizedEmail
          });
          results.added.push(normalizedEmail);
        } else {
          results.added.push(normalizedEmail);
        }
      } else {
        results.failed.push(email);
      }
    } catch (error) {
      results.failed.push(email);
    }
  }
  return results;
}

export async function removeEmailFromWhitelist(email: string): Promise<boolean> {
  const normalizedEmail = normalizeEmail(email);
  
  try {
    const result = await Whitelist.destroy({
      where: { email: normalizedEmail }
    });
    
    return result > 0;
  } catch (error) {
    console.error('Error removing email from whitelist:', error);
    return false;
  }
}

export async function getWhitelist(): Promise<string[]> {
  try {
    const entries = await Whitelist.findAll({
      attributes: ['email'],
      order: [['email', 'ASC']]
    });
    
    const emails = entries.map(entry => entry.getDataValue('email') as string);
    
    return emails.filter(Boolean);
  } catch (error) {
    console.error('Error getting whitelist:', error);
    return [];
  }
} 