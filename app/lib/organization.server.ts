import { Organization, ProblemSet, OrganizationUser, User, Test } from "~/models";
import { v4 as uuidv4 } from "uuid";

export class OrganizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "OrganizationError";
  }
}

export async function createOrganization(
  name: string,
  problemSetId: string,
  customId?: string,
  isOpenRegistration: boolean = false
) {
  // Verify the problem set exists
  const problemSet = await ProblemSet.findByPk(problemSetId);
  if (!problemSet) {
    throw new OrganizationError("Problem set not found");
  }

  // Use custom ID if provided, otherwise generate a UUID
  const id = customId || uuidv4();

  // Check if an organization with this ID already exists
  if (customId) {
    const existingOrg = await Organization.findByPk(customId);
    if (existingOrg) {
      throw new OrganizationError(
        `Organization with ID ${customId} already exists`
      );
    }
  }

  const organization = await Organization.create({
    id,
    name,
    problemSetId,
    isOpenRegistration,
  });

  return organization;
}

export async function getOrganization(id: string) {
  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }
  return organization;
}

export async function updateOrganizationProblemSet(
  id: string,
  problemSetId: string
) {
  // Verify the problem set exists
  const problemSet = await ProblemSet.findByPk(problemSetId);
  if (!problemSet) {
    throw new OrganizationError("Problem set not found");
  }

  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  organization.problemSetId = problemSetId;
  await organization.save();

  return organization;
}

export async function getOrganizationProblemSetId(
  orgId: string
): Promise<string> {
  const organization = await Organization.findByPk(orgId);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  return organization.problemSetId;
}

export async function deleteOrganization(id: string) {
  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  await organization.destroy();
  return true;
}

export async function isOrganizationOpenRegistration(orgId: string): Promise<boolean> {
  const organization = await Organization.findByPk(orgId);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  return organization.isOpenRegistration;
}

export async function addUserToOrganization(organizationId: string, userId: string): Promise<void> {
  // Check if the association already exists
  const existing = await OrganizationUser.findOne({
    where: { organizationId, userId }
  });

  if (!existing) {
    await OrganizationUser.create({
      id: uuidv4(),
      organizationId,
      userId,
    });
  }
}

export async function getOrganizationMembers(organizationId: string) {
  const organizationUsers = await OrganizationUser.findAll({
    where: { organizationId },
    include: [
      {
        model: User,
        attributes: ['id', 'name', 'email', 'createdAt']
      }
    ],
    order: [['createdAt', 'DESC']]
  });

  // Get tests for each user
  const membersWithTests = await Promise.all(
    organizationUsers.map(async (orgUser) => {
      const user = orgUser.User;
      const tests = await Test.findAll({
        where: { userId: user.id },
        attributes: ['id', 'url', 'createdAt'],
        order: [['createdAt', 'DESC']]
      });

      return {
        id: user.id,
        name: user.name,
        email: user.email,
        joinedAt: orgUser.createdAt,
        tests: tests.map(test => ({
          id: test.id,
          url: test.url,
          createdAt: test.createdAt
        }))
      };
    })
  );

  return membersWithTests;
}
