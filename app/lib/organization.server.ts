import { Organization, ProblemSet } from "~/models";
import { v4 as uuidv4 } from "uuid";

export class OrganizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "OrganizationError";
  }
}

export async function createOrganization(
  name: string,
  problemSetId: string,
  customId?: string
) {
  // Verify the problem set exists
  const problemSet = await ProblemSet.findByPk(problemSetId);
  if (!problemSet) {
    throw new OrganizationError("Problem set not found");
  }

  // Use custom ID if provided, otherwise generate a UUID
  const id = customId || uuidv4();

  // Check if an organization with this ID already exists
  if (customId) {
    const existingOrg = await Organization.findByPk(customId);
    if (existingOrg) {
      throw new OrganizationError(
        `Organization with ID ${customId} already exists`
      );
    }
  }

  const organization = await Organization.create({
    id,
    name,
    problemSetId,
  });

  return organization;
}

export async function getOrganization(id: string) {
  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }
  return organization;
}

export async function updateOrganizationProblemSet(
  id: string,
  problemSetId: string
) {
  // Verify the problem set exists
  const problemSet = await ProblemSet.findByPk(problemSetId);
  if (!problemSet) {
    throw new OrganizationError("Problem set not found");
  }

  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  organization.problemSetId = problemSetId;
  await organization.save();

  return organization;
}

export async function getOrganizationProblemSetId(
  orgId: string
): Promise<string> {
  const organization = await Organization.findByPk(orgId);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  return organization.problemSetId;
}

export async function deleteOrganization(id: string) {
  const organization = await Organization.findByPk(id);
  if (!organization) {
    throw new OrganizationError("Organization not found");
  }

  await organization.destroy();
  return true;
}
