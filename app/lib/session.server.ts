import { createCookieSessionStorage, redirect } from '@remix-run/node'

const ADMIN_PASSWORD = 'aXiAa3036'
const SESSION_SECRET = process.env.SESSION_SECRET || 'axiia-admin-session-secret'

const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: '__axiia_admin_session',
    httpOnly: true,
    maxAge: 60 * 60 * 24,
    path: '/',
    sameSite: 'lax',
    secrets: [SESSION_SECRET],
    secure: process.env.NODE_ENV === 'production',
  },
})

export async function getSession(request: Request) {
  const cookie = request.headers.get('Cookie')
  return sessionStorage.getSession(cookie)
}

export async function createAdminSession(request: Request, redirectTo: string) {
  const session = await getSession(request)
  session.set('isAdmin', true)
  session.set('adminAccessTime', new Date().toISOString())

  return redirect(redirectTo, {
    headers: {
      'Set-Cookie': await sessionStorage.commitSession(session),
    },
  })
}

export async function requireAdminAccess(request: Request) {
  const session = await getSession(request)

  if (!session.has('isAdmin')) {
    return false
  }

  const accessTime = new Date(session.get('adminAccessTime'))
  const now = new Date()
  const dayInMs = 24 * 60 * 60 * 1000

  if (now.getTime() - accessTime.getTime() > dayInMs) {
    throw redirect('/pea-admin', {
      headers: {
        'Set-Cookie': await sessionStorage.destroySession(session),
      },
    })
  }

  return true
}

export function verifyAdminPassword(password: string): boolean {
  return password === ADMIN_PASSWORD
}

export async function logout(request: Request) {
  const session = await getSession(request)
  return redirect('/pea-admin', {
    headers: {
      'Set-Cookie': await sessionStorage.destroySession(session),
    },
  })
}
