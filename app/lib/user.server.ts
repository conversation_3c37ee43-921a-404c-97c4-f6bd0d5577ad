import { nanoid } from 'nanoid';
import { validateEmail, formatPhoneNumber, normalizeEmail } from './utils';
import { User } from '~/models';
import type { UserRegistration } from './types';

export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export async function findOrCreateUser(registration: UserRegistration) {
  // Validate and normalize input
  if (!registration.name.trim()) {
    throw new ValidationError('Name is required');
  }

  let normalizedEmail: string | undefined;
  let normalizedPhone: string | undefined;

  if (registration.email) {
    if (!validateEmail(registration.email)) {
      throw new ValidationError('Invalid email format');
    }
    normalizedEmail = normalizeEmail(registration.email);
  }

  if (registration.phoneNumber) {
    normalizedPhone = formatPhoneNumber(registration.phoneNumber);
    if (!normalizedPhone) {
      throw new ValidationError('Invalid phone number format');
    }
  }

  if (!normalizedEmail && !normalizedPhone) {
    throw new ValidationError('Either email or phone number is required');
  }

  // Try to find existing user
  let user = await User.findOne({
    where: {
      [normalizedEmail ? 'email' : 'phoneNumber']: normalizedEmail || normalizedPhone,
    },
  });

  if (!user) {
    // Create new user
    user = await User.create({
      id: nanoid(),
      name: registration.name,
      email: normalizedEmail,
      phoneNumber: normalizedPhone,
    });
  }

  return user;
} 