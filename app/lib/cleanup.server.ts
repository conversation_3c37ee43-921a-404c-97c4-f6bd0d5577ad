import { cleanupExpiredCodes } from './email.server';

const CLEANUP_INTERVAL = 60 * 60 * 1000;

let cleanupInterval: NodeJS.Timeout | null = null;

export function startCleanupTask() {
  if (cleanupInterval) {
    return;
  }

  runCleanup();
  cleanupInterval = setInterval(runCleanup, CLEANUP_INTERVAL);
  console.log(`Verification code cleanup task started, running every ${CLEANUP_INTERVAL / 1000 / 60} minutes`);
}


export function stopCleanupTask() {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
    console.log('Verification code cleanup task stopped');
  }
}

async function runCleanup() {
  try {
    const cleanedCount = await cleanupExpiredCodes();
    console.log(`Cleanup task completed: removed ${cleanedCount} expired verification codes`);
  } catch (error) {
    console.error('Error during verification code cleanup:', error);
  }
} 