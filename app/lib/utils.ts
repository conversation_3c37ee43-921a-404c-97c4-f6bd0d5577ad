import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function formatPhoneNumber(phone: string): string | undefined {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone.trim())? phone.trim(): undefined
}

export function normalizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface AnyObj<T = any> {
  [key: string]: T
}

export const dateFormat = (input: string | Date | number, format = 'yyyy-MM-dd hh:mm:ss') => {
  if (typeof input === 'string') {
    if (/^\d+$/.test(input)) {
      input = parseInt(input, 10)
    }
  }
  const date = new Date(input)
  if (!date || date.toUTCString() === 'Invalid Date') {
    console.error(`dateFormat first param is invalid`)
    return ''
  }
  const map: AnyObj<number> = {
    M: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    m: date.getMinutes(),
    s: date.getSeconds(),
    q: Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  }
  format = format.replace(/([yMdhmsqS])+/g, function (all, t) {
    let v: number | undefined | string = map[t] as number | undefined
    if (v !== undefined) {
      if (all.length > 1) {
        v = '0' + v
        v = v.substr(v.length - 2)
      }
      return v as string
    } else if (t === 'y') {
      return ((date as Date).getFullYear() + '').substr(4 - all.length)
    }
    return all
  })
  return format
}
