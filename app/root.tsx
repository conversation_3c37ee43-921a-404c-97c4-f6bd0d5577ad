import {
  Links,
  Meta,
  <PERSON>let,
  <PERSON><PERSON>ts,
  ScrollRestoration,
  isRouteErrorResponse,
  useRouteError,
} from '@remix-run/react'
import type { LinksFunction } from '@remix-run/node'

import styles from './tailwind.css?url'

export const links: LinksFunction = () => [{ rel: 'stylesheet', href: styles }]

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export function ErrorBoundary() {
  const error = useRouteError()

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center px-6">
          <h1 className="text-4xl font-light text-gray-900 mb-4">
            {isRouteErrorResponse(error) && error.status === 404 ? '页面未找到' : '发生错误'}
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            {isRouteErrorResponse(error) && error.status === 404
              ? '您访问的页面不存在。如需帮助，请联系管理员。'
              : '抱歉，系统出现了问题。请稍后再试或联系管理员。'}
          </p>
          <a
            href="/"
            className="inline-block px-6 py-3 rounded-md bg-gray-900 text-white text-sm hover:bg-gray-800 transition-colors"
          >
            返回首页
          </a>
        </div>
        <Scripts />
      </body>
    </html>
  )
}

export default function App() {
  return <Outlet />
}
