import {
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
} from "@remix-run/node";
import {
  Form,
  NavLink,
  Outlet,
  useActionData,
  useLoaderData,
} from "@remix-run/react";
import { Button } from "~/components/ui/button";
import { BookOpen, Building2, Loader2, LogOut, Users } from "lucide-react";
import {
  createAdminSession,
  requireAdminAccess,
  verifyAdminPassword,
} from "~/lib/session.server";
import { useSubmitting } from "~/components/hooks/use-submitting";
import { useState } from "react";

const adminMenuItems = [
  { path: "/pea-admin/problemset", label: "Problem Sets", icon: BookOpen },
  { path: "/pea-admin/organizations", label: "Organizations", icon: Building2 },
  { path: "/pea-admin/whitelist", label: "Whitelist", icon: Users },
];

export async function loader({ request }: LoaderFunctionArgs) {
  const isAdmin = await requireAdminAccess(request);

  return Response.json({ isAdmin });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const password = formData.get("password")?.toString() || "";

  if (!verifyAdminPassword(password)) {
    return Response.json({ error: "Invalid password" }, { status: 401 });
  }

  return createAdminSession(request, "/pea-admin/whitelist");
}

export default function PeaAdmin() {
  const { isAdmin } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const isSubmitting = useSubmitting();
  const [password, setPassword] = useState("");

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="w-full max-w-md bg-white rounded-2xl shadow-sm">
          <div className="p-6 sm:p-8">
            <div className="space-y-6">
              <div className="text-center">
                <h1 className="text-2xl font-medium text-gray-900">
                  Admin Access
                </h1>
                <p className="mt-2 text-sm text-gray-500">
                  Enter password to access admin area
                </p>
              </div>

              <Form method="post" className="space-y-4">
                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting
                    ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Verifying...
                      </>
                    )
                    : (
                      "Access Admin Area"
                    )}
                </Button>
              </Form>

              {actionData?.error && (
                <div className="flex items-center gap-2 text-red-600 text-sm p-3 bg-red-50 rounded-lg">
                  <div className="w-1 h-4 bg-red-600 rounded-full" />
                  <p>{actionData.error}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex overflow-hidden bg-gray-50">
      {/* navbar */}
      <nav className="w-64 bg-white shadow-sm hidden md:block overflow-y-auto">
        <div className="h-full flex flex-col">
          <div className="p-4 border-b">
            <h1 className="text-md font-semibold text-gray-900">
              Axiia Administrator System
            </h1>
          </div>

          <div className="flex-1 py-6 px-4 space-y-1">
            {adminMenuItems.map((item) => {
              const Icon = item.icon;

              return (
                <NavLink
                  key={item.path}
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center px-3 py-2 rounded-lg transition ${
                      isActive
                        ? "bg-primary/10 text-primary"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  <span>{item.label}</span>
                </NavLink>
              );
            })}
          </div>

          <div className="p-4 border-t">
            <div className="flex items-center justify-end text-sm">
              <Form action="/logout" method="post">
                <button
                  type="submit"
                  className="text-gray-500 hover:text-gray-700 flex items-center"
                >
                  <LogOut className="w-4 h-4 mr-1" />
                  Logout
                </button>
              </Form>
            </div>
          </div>
        </div>
      </nav>

      <div className="md:hidden fixed top-0 left-0 right-0 bg-white shadow-sm z-10">
        <div className="flex items-center justify-between px-4 py-3">
          <h1 className="font-semibold text-gray-900">
            Axiia Administrator system
          </h1>

          <Form action="/logout" method="post">
            <button
              type="submit"
              className="text-gray-500 hover:text-gray-700 flex items-center"
            >
              <LogOut className="w-4 h-4 mr-1" />
              <span className="text-sm">Logout</span>
            </button>
          </Form>
        </div>

        <div className="flex overflow-x-auto px-4 pb-3 border-b">
          {adminMenuItems.map((item) => {
            const Icon = item.icon;

            return (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center px-3 py-1 rounded-lg mr-2 transition whitespace-nowrap text-sm ${
                    isActive
                      ? "bg-primary/10 text-primary"
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
              >
                <Icon className="w-4 h-4 mr-1" />
                <span>{item.label}</span>
              </NavLink>
            );
          })}
        </div>
      </div>

      <main className="flex-1 relative overflow-y-auto focus:outline-none">
        <div className="md:py-6 md:px-6 px-4 py-24">
          <Outlet />
        </div>
      </main>
    </div>
  );
}
