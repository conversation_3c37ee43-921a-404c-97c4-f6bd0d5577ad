import {
  Form,
  useActionData,
  useLoaderData,
  useNavigate,
} from "@remix-run/react";
import { But<PERSON> } from "~/components/ui/button";
import { useState } from "react";
import { createProblemSet, fetchProblems } from "~/lib/problem-set.server";
import { SortableProblems } from "~/components/sortable-problems";
import { useSubmitting } from "~/components/hooks/use-submitting";
import { Loader2 } from "lucide-react";
import { CopyButton } from "~/components/copyable-button";
import { type ActionFunctionArgs } from "@remix-run/node";

export async function loader() {
  const problems = await fetchProblems();
  return Response.json({ problems });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const problemIds = formData.getAll("problemId").map((p) => p.toString());
  const expireMinutes = formData.get("expireMinutes");
  if (problemIds.length === 0) {
    return Response.json({ error: "Please select at least one problem" }, {
      status: 400,
    });
  }
  const problemSetId = await createProblemSet(
    problemIds,
    expireMinutes ? parseInt(expireMinutes.toString()) : undefined,
  );

  return Response.json({ success: true, problemSetId });
}

export default function NewProblemSet() {
  const { problems } = useLoaderData<typeof loader>();
  const [selectedProblems, setSelectedProblems] = useState<string[]>([]);
  const isSubmitting = useSubmitting();
  const actionData = useActionData<typeof action>();
  const navigate = useNavigate();

  return (
    <div className="border rounded-lg bg-white p-6 shadow-sm">
      <h2 className="text-xl font-semibold mb-4">
        Create Problem Set
      </h2>

      <div className="space-y-6">
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-medium text-gray-900">
              Available Problems
            </h3>
            <span className="text-sm text-gray-500">
              {selectedProblems.length} selected
            </span>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            {problems.map((problemId: string) => (
              <label
                key={problemId}
                className={`flex items-center p-3 sm:p-4 border rounded-lg cursor-pointer transition text-sm
                ${
                  selectedProblems.includes(problemId)
                    ? "border-primary bg-primary/10"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <input
                  type="checkbox"
                  value={problemId}
                  checked={selectedProblems.includes(
                    problemId,
                  )}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedProblems([
                        ...selectedProblems,
                        problemId,
                      ]);
                    } else {
                      setSelectedProblems(
                        selectedProblems.filter((
                          p,
                        ) => p !== problemId),
                      );
                    }
                  }}
                  className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary accent-primary"
                />
                <span className="ml-3 font-medium text-gray-900 break-all">
                  {problemId}
                </span>
              </label>
            ))}
          </div>
        </div>

        {selectedProblems.length > 0 && (
          <div className="pt-6 border-t border-gray-200">
            <h3 className="text-base font-medium text-gray-900 mb-4">
              Selected Problems (Drag to reorder)
            </h3>
            <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
              <SortableProblems
                problems={selectedProblems}
                onChange={setSelectedProblems}
              />
            </div>
          </div>
        )}

        <Form
          method="post"
          className="pt-6"
        >
          <div className="pt-6 border-t border-gray-200 mb-6">
            <h3 className="text-base font-medium text-gray-900 mb-4">
              Expiration (optional)
            </h3>
            <div className="flex items-center gap-2">
              <input
                type="number"
                name="expireMinutes"
                className="w-24 border border-gray-200 rounded-lg p-2"
              />
              <span className="text-sm text-gray-500">
                minutes
              </span>
            </div>
          </div>

          {selectedProblems.map((problemId, index) => (
            <input
              key={index}
              type="hidden"
              name="problemId"
              value={problemId}
            />
          ))}

          <div className="flex items-center gap-2">
            <Button
              type="submit"
              disabled={selectedProblems.length === 0 ||
                isSubmitting}
            >
              {isSubmitting
                ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                )
                : (
                  "Create Problem Set"
                )}
            </Button>
            <Button
              variant="outline"
              type="button"
              onClick={() => {
                navigate("/pea-admin/problemset");
              }}
            >
              Back
            </Button>
          </div>
        </Form>

        {actionData?.success && (
          <div className="mt-6 p-4 border border-green-100 bg-green-50 rounded-lg space-y-4">
            <h3 className="text-base font-medium text-gray-900">
              Problem Set Created! You can go to organization page to update the
              problem set.
            </h3>
            <div className="flex items-center gap-2">
              <input
                type="text"
                readOnly
                value={actionData.problemSetId}
                className="flex-1 px-3 py-2 bg-white border border-gray-200 rounded-lg font-mono text-sm overflow-x-auto"
              />
              <CopyButton
                text={actionData.problemSetId}
                className="w-4 h-4"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
