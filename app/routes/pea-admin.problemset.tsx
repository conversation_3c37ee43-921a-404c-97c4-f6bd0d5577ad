import { Link, Outlet, useLoaderData } from "@remix-run/react";
import { fetchAllProblemSets } from "~/lib/problem-set.server";
import { ExternalLink, Info, Plus } from "lucide-react";
import { Button } from "~/components/ui/button";
import type { ProblemSet } from "~/lib/types";
import { useLocation } from "@remix-run/react";
import { useState } from "react";
import { CopyButton } from "~/components/copyable-button";

export async function loader() {
  const problemSets = await fetchAllProblemSets();
  return Response.json({ problemSets });
}

export default function ProblemSetList() {
  const { problemSets } = useLoaderData<typeof loader>();
  const [activeTooltip, setActiveTooltip] = useState<string | null>(null);
  const location = useLocation();
  const isIndex = location.pathname === "/pea-admin/problemset";


  return isIndex
    ? (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Problem Sets</h1>
            <p className="text-muted-foreground">
              Manage assessment problem sets
            </p>
          </div>
          <Link to="/pea-admin/problemset/new">
            <Button className="flex items-center gap-1">
              <Plus className="w-4 h-4" />
              <span>Add Problem Set</span>
            </Button>
          </Link>
        </div>

        <div className="border rounded-lg bg-white overflow-hidden shadow-sm">
          {problemSets.length === 0
            ? (
              <div className="p-8 text-center text-gray-500">
                <p>No problem sets found</p>
                <Link
                  to="/pea-admin/problemset/new"
                  className="text-primary hover:underline mt-2 inline-block"
                >
                  Create your first problem set
                </Link>
              </div>
            )
            : (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-gray-50 border-b text-left">
                      <th className="px-6 py-3 text-gray-500 font-medium">
                        ID
                      </th>
                      <th className="px-6 py-3 text-gray-500 font-medium">
                        Problems
                      </th>
                      <th className="px-6 py-3 text-gray-500 font-medium">
                        Created At
                      </th>
                      <th className="px-6 py-3 text-gray-500 font-medium">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {problemSets.map((set: ProblemSet) => (
                      <tr key={set.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 font-mono text-xs break-all">
                          {set.id}
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <span className="font-medium">
                              {set.problemIds.length}
                            </span>
                            <span className="text-gray-500 ml-1">problems</span>
                            <div className="relative ml-2">
                              <button
                                className="text-gray-400 hover:text-primary"
                                onMouseEnter={() =>
                                  setActiveTooltip(set.id)}
                                onMouseLeave={() =>
                                  setActiveTooltip(null)}
                              >
                                <Info className="w-4 h-4" />
                              </button>

                              {activeTooltip === set.id && (
                                <div className="absolute z-10 mt-2 -left-4 w-64 p-4 bg-white rounded-lg shadow-lg border border-gray-200 text-xs">
                                  <h4 className="font-medium text-gray-900 mb-2">
                                    Problem IDs:
                                  </h4>
                                  <ul className="space-y-1 max-h-[200px] overflow-y-auto">
                                    {set.problemIds.map((id, index) => (
                                      <li
                                        key={index}
                                        className="font-mono text-gray-700 flex"
                                      >
                                        <span className="text-gray-500 w-6 flex-shrink-0">
                                          {index + 1}.
                                        </span>
                                        <span className="break-all">{id}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-gray-500">
                          <div className="flex items-center gap-1">
                            {new Date(String(set.createdAt)).toLocaleString()}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <CopyButton
                            text={set.id}
                            className="w-4 h-4"
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
        </div>
      </div>
    )
    : <Outlet />;
}
