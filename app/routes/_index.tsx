import type { MetaFunction } from '@remix-run/node'
import { useScreenSize } from '~/components/hooks/use-screen-size'
import { PixelTrail } from '~/components/ui/pixel-trail'

export const meta: MetaFunction = () => {
  return [
    { title: 'Axiia.ai' },
    { name: 'description', content: 'Unlock Your AI Potential with Axiia.' },
  ]
}

export default function Index() {
  const screenSize = useScreenSize()

  return (
    <div className="relative w-full h-screen bg-[#1A1A1A] text-white flex flex-col font-calendas">
      <div className="fixed inset-0 z-0">
        <PixelTrail
          pixelSize={screenSize.lessThan(`lg`) ? 48 : 80}
          fadeDuration={0}
          delay={1200}
          pixelClassName="rounded-full bg-[#25A04A]"
        />
      </div>

      <div className="justify-center items-center flex flex-col w-full h-full z-10 pointer-events-none space-y-2 md:space-y-8">
        <h2 className="text-3xl cursor-pointer sm:text-5xl md:text-7xl tracking-tight">Axiia AI</h2>
        <p className="text-xs md:text-2xl">Unlock Your AI Potential with Axiia.</p>
      </div>

      <p className="absolute text-xs md:text-base bottom-4 right-4 pointer-events-none">
        AI-powered insights for your business.
      </p>
    </div>
  )
}
