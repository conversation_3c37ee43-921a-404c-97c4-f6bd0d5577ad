import { type LoaderFunctionArgs } from '@remix-run/node'
import { useLoaderData, Link } from '@remix-run/react'
import { Button } from '~/components/ui/button'
import { ArrowLeft, Users, TestTube } from 'lucide-react'
import { Organization } from '~/models'
import { getOrganizationMembers } from '~/lib/organization.server'
import { requireAdminAccess } from '~/lib/session.server'

interface MemberData {
  id: string
  name: string
  email: string
  joinedAt: Date
  tests: {
    id: string
    url: string
    createdAt: Date
  }[]
}

interface OrganizationData {
  id: string
  name: string
  isOpenRegistration: boolean
}

export async function loader({ request, params }: LoaderFunctionArgs) {
  // Check if user has admin access
  await requireAdminAccess(request)

  const { orgId } = params
  if (!orgId) {
    throw new Response('Organization ID is required', { status: 400 })
  }

  // Get organization details
  const organization = await Organization.findByPk(orgId)
  if (!organization) {
    throw new Response('Organization not found', { status: 404 })
  }

  // Get organization members
  const members = await getOrganizationMembers(orgId)

  return Response.json({
    organization: {
      id: organization.id,
      name: organization.name,
      isOpenRegistration: organization.isOpenRegistration,
    },
    members,
  })
}

export default function OrganizationMembers() {
  const { organization, members } = useLoaderData<{
    organization: OrganizationData
    members: MemberData[]
  }>()

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link to="/pea-admin/organizations">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Organizations
              </Button>
            </Link>
          </div>
          
          <div className="flex items-center gap-3">
            <Users className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {organization.name} - Members
              </h1>
              <div className="flex items-center gap-2 mt-1">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  organization.isOpenRegistration 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {organization.isOpenRegistration ? 'Open Registration' : 'Whitelist Only'}
                </span>
                <span className="text-sm text-gray-500">
                  {members.length} member{members.length !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Members Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Organization Members</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Member
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tests
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {members.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="px-6 py-8 text-center text-sm text-gray-500">
                      <Users className="w-12 h-12 mx-auto text-gray-300 mb-4" />
                      <p>No members found for this organization</p>
                      {organization.isOpenRegistration && (
                        <p className="text-xs text-gray-400 mt-1">
                          Members will appear here when they register through the organization URL
                        </p>
                      )}
                    </td>
                  </tr>
                ) : (
                  members.map((member) => (
                    <tr key={member.id}>
                      <td className="px-6 py-4 text-sm font-medium text-gray-900">
                        {member.name}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {member.email}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {new Date(member.joinedAt).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {member.tests.length === 0 ? (
                          <span className="text-gray-400">No tests</span>
                        ) : (
                          <div className="space-y-1">
                            {member.tests.map((test) => (
                              <div key={test.id} className="flex items-center gap-2">
                                <TestTube className="w-3 h-3 text-blue-500" />
                                <a
                                  href={test.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 text-xs"
                                >
                                  Test {test.id.slice(0, 8)}...
                                </a>
                                <span className="text-xs text-gray-400">
                                  {new Date(test.createdAt).toLocaleDateString()}
                                </span>
                              </div>
                            ))}
                          </div>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Organization URL Info */}
        {organization.isOpenRegistration && (
          <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-green-900 mb-2">
              Open Registration URL
            </h3>
            <p className="text-sm text-green-700 mb-3">
              Users can register directly using this URL without being in the whitelist:
            </p>
            <div className="bg-white border border-green-300 rounded px-3 py-2 font-mono text-sm">
              {typeof window !== 'undefined' ? window.location.origin : ''}/login?id={organization.id}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
