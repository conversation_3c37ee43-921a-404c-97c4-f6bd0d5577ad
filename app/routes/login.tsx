import { useEffect, useState } from "react";
import {
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
  redirect,
} from "@remix-run/node";
import {
  Form,
  useActionData,
  useLoaderData,
  useNavigation,
  useSubmit,
} from "@remix-run/react";
import { But<PERSON> } from "~/components/ui/button";
import { Loader2, Mail } from "lucide-react";
import {
  EmailVerificationError,
  generateVerificationCode,
  isEmailInWhitelist,
  sendVerificationEmail,
  verifyCode,
} from "~/lib/email.server";
import { findOrCreateUser, ValidationError } from "~/lib/user.server";
import { createTest, getUserTests } from "~/lib/test.server";
import { User } from "~/lib/types";
import {
  getOrganizationProblemSetId,
  OrganizationError,
} from "~/lib/organization.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const orgId = url.searchParams.get("id");
  const hasVerificationSent =
    url.searchParams.get("verificationSent") === "true";
  const emailParam = url.searchParams.get("email") || "";

  if (!orgId) {
    return redirect("/");
  }

  return Response.json({
    orgId,
    initialVerificationSent: hasVerificationSent,
    initialEmail: emailParam,
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const orgId = formData.get("orgId")?.toString();
  const email = formData.get("email")?.toString();
  const action = formData.get("action")?.toString();
  const verificationCode = formData.get("verificationCode")?.toString();

  if (!orgId) {
    return Response.json({ error: "Missing organization ID" }, {
      status: 400,
    });
  }

  // 1: Send verification code
  if (action === "send-code" && email) {
    try {
      const isInWhitelist = await isEmailInWhitelist(email);
      if (!isInWhitelist) {
        return Response.json(
          {
            error: "邮件地址不在白名单中，请联系管理员添加。",
          },
          { status: 403 },
        );
      }

      const code = await generateVerificationCode(email);

      const sent = await sendVerificationEmail(email, code);

      if (!sent) {
        return Response.json(
          {
            error: "Failed to send verification code. Please try again.",
          },
          { status: 500 },
        );
      }

      return Response.json({
        success: true,
        message: "验证码发送成功",
        email,
      });
    } catch (error) {
      if (error instanceof EmailVerificationError) {
        return Response.json({ error: error.message }, { status: 400 });
      }
      console.error("Error sending verification code:", error);
      return Response.json(
        { error: "发生意外错误，请稍后再试。" },
        { status: 500 },
      );
    }
  }

  // 2: Verify code and register/login user
  if (action === "verify-code" && email && verificationCode) {
    try {
      const isCodeValid = await verifyCode(email, verificationCode);

      if (!isCodeValid) {
        return Response.json(
          {
            error: "验证码无效或已过期，请重新输入。",
          },
          { status: 400 },
        );
      }

      // Get the problem set ID associated with the organization
      let problemSetId;
      try {
        problemSetId = await getOrganizationProblemSetId(orgId);
      } catch (error) {
        if (error instanceof OrganizationError) {
          return Response.json({ error: error.message }, { status: 400 });
        }
        console.error("Error fetching organization:", error);
        return Response.json(
          { error: "An error occurred while retrieving the assessment" },
          { status: 500 },
        );
      }

      const user = await findOrCreateUser({
        name: "Lenovo",
        email,
      }) as User;

      const tests = await getUserTests(user.id);

      const url = tests.length
        ? tests[0].url
        : (await createTest(user.id, problemSetId)).url;

      return redirect(url);
    } catch (error) {
      if (
        error instanceof ValidationError ||
        error instanceof EmailVerificationError
      ) {
        return Response.json({ error: error.message }, { status: 400 });
      }
      console.error("Error during verification:", error);
      return Response.json(
        { error: "An unexpected error occurred" },
        { status: 500 },
      );
    }
  }

  return Response.json({ error: "Invalid action" }, { status: 400 });
}

export default function Login() {
  const { orgId, initialVerificationSent, initialEmail } = useLoaderData<
    typeof loader
  >();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const submit = useSubmit();
  const isSubmitting = navigation.state === "submitting";

  const [verificationSent, setVerificationSent] = useState(
    initialVerificationSent || false,
  );
  const [email, setEmail] = useState(initialEmail || "");
  const [emailInput, setEmailInput] = useState(initialEmail || "");

  useEffect(() => {
    if (actionData?.email && actionData.success) {
      setEmail(actionData.email);
      setVerificationSent(true);

      const url = new URL(window.location.href);
      url.searchParams.set("verificationSent", "true");
      url.searchParams.set("email", actionData.email);
      window.history.replaceState({}, "", url.toString());
    }
  }, [actionData]);

  const handleChangeEmail = () => {
    setVerificationSent(false);

    const url = new URL(window.location.href);
    url.searchParams.delete("verificationSent");
    window.history.replaceState({}, "", url.toString());
  };

  const handleResendCode = () => {
    if (!email) return;

    const formData = new FormData();
    formData.append("orgId", orgId);
    formData.append("email", email);
    formData.append("action", "send-code");

    submit(formData, { method: "post" });
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmailInput(e.target.value);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-lg bg-white rounded-2xl shadow-sm">
        <div className="p-4 sm:p-8">
          <div className="space-y-6 sm:space-y-8">
            <div className="text-center">
              <h1 className="text-xl sm:text-2xl font-medium text-gray-900">
                Axiia AI 能力评估
              </h1>
              <p className="mt-2 text-xs sm:text-sm text-gray-500">
                {!verificationSent
                  ? "请输入您的企业邮箱"
                  : "输入您收到的6位验证码"}
              </p>
            </div>

            {!verificationSent
              ? (
                <Form method="post" className="space-y-6">
                  <input
                    type="hidden"
                    name="orgId"
                    value={orgId}
                  />
                  <input
                    type="hidden"
                    name="action"
                    value="send-code"
                  />

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700"
                    >
                      邮箱
                    </label>
                    <div className="mt-1 relative">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                        <Mail className="w-5 h-5" />
                      </span>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        required
                        autoComplete="email"
                        value={emailInput}
                        onChange={handleEmailChange}
                        placeholder="<EMAIL>"
                        className="block w-full pl-10 pr-3 py-2 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition text-sm sm:text-base"
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full py-2"
                    disabled={isSubmitting}
                  >
                    {isSubmitting
                      ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          发送中...
                        </>
                      )
                      : (
                        "发送验证码"
                      )}
                  </Button>
                </Form>
              )
              : (
                <Form method="post" className="space-y-6">
                  <input
                    type="hidden"
                    name="orgId"
                    value={orgId}
                  />
                  <input
                    type="hidden"
                    name="action"
                    value="verify-code"
                  />
                  <input
                    type="hidden"
                    name="email"
                    value={email}
                  />

                  <div>
                    <div className="flex items-center justify-between">
                      <label
                        htmlFor="verificationCode"
                        className="block text-sm font-medium text-gray-700"
                      >
                        验证码
                      </label>
                      <button
                        type="button"
                        onClick={handleChangeEmail}
                        className="text-xs text-primary hover:text-primary/80"
                      >
                        更换邮箱
                      </button>
                    </div>
                    <input
                      id="verificationCode"
                      name="verificationCode"
                      type="text"
                      required
                      autoComplete="one-time-code"
                      placeholder="123456"
                      className="mt-1 block w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition text-sm sm:text-base"
                    />
                    <p className="mt-2 text-xs text-gray-500">
                      请输入您收到的6位验证码
                    </p>
                    <p className="mt-1 text-xs text-gray-400">
                      验证码将发送至：{email}
                    </p>
                  </div>

                  <Button
                    type="submit"
                    className="w-full py-2"
                    disabled={isSubmitting}
                  >
                    {isSubmitting
                      ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          验证中...
                        </>
                      )
                      : (
                        "确认"
                      )}
                  </Button>

                  <p className="text-xs text-center text-gray-500">
                    未收到验证码？{" "}
                    <button
                      type="button"
                      onClick={handleResendCode}
                      disabled={isSubmitting}
                      className="text-primary hover:text-primary/80 disabled:opacity-50"
                    >
                      重新发送
                    </button>
                  </p>
                </Form>
              )}

            {actionData?.error && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <div className="w-1 h-4 bg-red-600 rounded-full" />
                <p>{actionData.error}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
