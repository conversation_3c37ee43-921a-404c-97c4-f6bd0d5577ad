import { useState } from 'react'
import { type ActionFunctionArgs, type LoaderFunctionArgs } from '@remix-run/node'
import { Form, useActionData, useLoaderData, useNavigation, Link } from '@remix-run/react'
import { Button } from '~/components/ui/button'
import { Co<PERSON>, Loader2, Plus, Settings, Trash, Users } from 'lucide-react'
import { Organization, ProblemSet } from '~/models'
import {
  createOrganization,
  deleteOrganization,
  OrganizationError,
  updateOrganizationProblemSet,
} from '~/lib/organization.server'
import { requireAdminAccess } from '~/lib/session.server'
import { Input } from '~/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Checkbox } from '~/components/ui/checkbox'

// Define interfaces for the data structure
interface ProblemSetData {
  id: string
  createdAt: Date
}

interface OrganizationData {
  id: string
  name: string
  problemSetId: string
  isOpenRegistration: boolean
  createdAt: Date
}

// Component for creating a new organization
interface CreateOrganizationFormProps {
  problemSets: ProblemSetData[]
  isSubmitting: boolean
  onCancel: () => void
}

function CreateOrganizationForm({
  problemSets,
  isSubmitting,
  onCancel,
}: CreateOrganizationFormProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6 mb-8">
      <h2 className="text-lg font-medium mb-4">Create New Organization</h2>
      <Form method="post" className="space-y-4">
        <input type="hidden" name="action" value="create" />

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Organization Name
          </label>
          <Input id="name" name="name" type="text" required placeholder="Enter organization name" />
        </div>

        <div>
          <label htmlFor="customId" className="block text-sm font-medium text-gray-700 mb-1">
            Custom ID (Optional)
          </label>
          <Input
            id="customId"
            name="customId"
            type="text"
            placeholder="Enter custom ID (e.g. existing problem set ID)"
          />
          <p className="mt-1 text-xs text-gray-500">
            Leave blank to generate an ID automatically, or enter a specific ID like an existing
            problem set ID
          </p>
        </div>

        <div>
          <label htmlFor="problemSetId" className="block text-sm font-medium text-gray-700 mb-1">
            Problem Set
          </label>
          <Select name="problemSetId" required defaultValue="">
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a problem set" />
            </SelectTrigger>
            <SelectContent>
              {problemSets.map((ps: ProblemSetData) => (
                <SelectItem key={ps.id} value={ps.id}>
                  {ps.id} (Created: {new Date(ps.createdAt).toLocaleString()})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Checkbox
            id="isOpenRegistration"
            name="isOpenRegistration"
            value="true"
            label="Open Registration (Allow users to register without whitelist)"
          />
          <p className="mt-1 text-xs text-gray-500">
            When enabled, users can register directly without being in the whitelist
          </p>
        </div>

        <div className="flex justify-end gap-3">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              'Create'
            )}
          </Button>
        </div>
      </Form>
    </div>
  )
}

// Component for editing a problem set
interface EditProblemSetFormProps {
  orgId: string
  currentProblemSetId: string
  problemSets: ProblemSetData[]
  isSubmitting: boolean
  onCancel: () => void
}

function EditProblemSetForm({
  orgId,
  currentProblemSetId,
  problemSets,
  isSubmitting,
  onCancel,
}: EditProblemSetFormProps) {
  return (
    <Form method="post" className="flex flex-col sm:flex-row gap-2">
      <input type="hidden" name="action" value="update" />
      <input type="hidden" name="id" value={orgId} />
      <Select name="problemSetId" defaultValue={currentProblemSetId}>
        <SelectTrigger className="w-48 text-sm">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {problemSets.map((ps: ProblemSetData) => (
            <SelectItem key={ps.id} value={ps.id}>
              {ps.id}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <div className="flex gap-2">
        <Button type="submit" size="sm" className="py-1 px-2 text-xs" disabled={isSubmitting}>
          {isSubmitting ? <Loader2 className="w-3 h-3 animate-spin" /> : 'Save'}
        </Button>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="py-1 px-2 text-xs"
          onClick={onCancel}
        >
          Cancel
        </Button>
      </div>
    </Form>
  )
}

// Component for the organizations table
interface OrganizationTableProps {
  organizations: OrganizationData[]
  problemSets: ProblemSetData[]
  isSubmitting: boolean
  editingOrgId: string | null
  onStartEdit: (orgId: string) => void
  onCancelEdit: () => void
}

function OrganizationTable({
  organizations,
  problemSets,
  isSubmitting,
  editingOrgId,
  onStartEdit,
  onCancelEdit,
}: OrganizationTableProps) {
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
              >
                Organization Name
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
              >
                Organization ID
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
              >
                Problem Set ID
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
              >
                Registration Type
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
              >
                Created
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {organizations.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                  No organizations found
                </td>
              </tr>
            ) : (
              organizations.map((org: OrganizationData) => (
                <tr key={org.id}>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                    {org.name}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 break-all">{org.id}</td>
                  <td className="px-6 py-4 text-sm text-gray-500 break-all">
                    {editingOrgId === org.id ? (
                      <EditProblemSetForm
                        orgId={org.id}
                        currentProblemSetId={org.problemSetId}
                        problemSets={problemSets}
                        isSubmitting={isSubmitting}
                        onCancel={onCancelEdit}
                      />
                    ) : (
                      <span>{org.problemSetId}</span>
                    )}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        org.isOpenRegistration
                          ? 'bg-green-100 text-green-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {org.isOpenRegistration ? 'Open Registration' : 'Whitelist Only'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                    {new Date(org.createdAt).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {editingOrgId !== org.id && (
                        <>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="py-1 px-2 text-xs"
                            onClick={() => onStartEdit(org.id)}
                          >
                            <Settings className="w-3 h-3" />
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="py-1 px-2 text-xs"
                            onClick={() => {
                              navigator.clipboard.writeText(
                                `${window.location.origin}/login?id=${org.id}`,
                              )
                            }}
                            title="Copy Login URL"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          <Link to={`/pea-admin/organization-members/${org.id}`}>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="py-1 px-2 text-xs"
                              title="View Members"
                            >
                              <Users className="w-3 h-3" />
                            </Button>
                          </Link>
                          <Form method="post">
                            <input type="hidden" name="action" value="delete" />
                            <input type="hidden" name="id" value={org.id} />
                            <Button
                              type="submit"
                              size="sm"
                              variant="outline"
                              disabled={isSubmitting}
                            >
                              {isSubmitting ? (
                                <>
                                  <Loader2 className="w-3 h-3 animate-spin" />
                                </>
                              ) : (
                                <Trash className="w-3 h-3 text-red-500" />
                              )}
                            </Button>
                          </Form>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}

// Message component for displaying action results
interface ActionMessageProps {
  success?: boolean
  error?: string
  message?: string
}

function ActionMessage({ success, error, message }: ActionMessageProps) {
  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
        <p className="text-red-700">{error}</p>
      </div>
    )
  }

  if (success && message) {
    return (
      <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
        <p className="text-green-700">{message}</p>
      </div>
    )
  }

  return null
}

export async function loader({ request }: LoaderFunctionArgs) {
  // Check if user has admin access
  await requireAdminAccess(request)

  // Fetch all organizations with their problem sets
  const organizations = await Organization.findAll({
    include: [{ model: ProblemSet }],
    order: [['createdAt', 'DESC']],
  })

  // Fetch all problem sets for the dropdown
  const problemSets = await ProblemSet.findAll({
    order: [['createdAt', 'DESC']],
  })

  return Response.json({
    organizations: organizations.map((org) => ({
      id: org.id,
      name: org.name,
      problemSetId: org.problemSetId,
      isOpenRegistration: org.isOpenRegistration,
      createdAt: org.createdAt,
    })),
    problemSets: problemSets.map((ps) => ({
      id: ps.id,
      createdAt: ps.createdAt,
    })),
  })
}

export async function action({ request }: ActionFunctionArgs) {
  // Check if user has admin access
  await requireAdminAccess(request)

  const formData = await request.formData()
  const action = formData.get('action')?.toString()

  // Create a new organization
  if (action === 'create') {
    const name = formData.get('name')?.toString()
    const problemSetId = formData.get('problemSetId')?.toString()
    const customId = formData.get('customId')?.toString()
    const isOpenRegistration = formData.get('isOpenRegistration') === 'true'

    if (!name || !problemSetId) {
      return Response.json({ error: 'Name and problem set are required' }, { status: 400 })
    }

    try {
      const organization = await createOrganization(
        name,
        problemSetId,
        customId,
        isOpenRegistration,
      )
      return Response.json({
        success: true,
        message: 'Organization created successfully',
        organization: {
          id: organization.id,
          name: organization.name,
          problemSetId: organization.problemSetId,
          isOpenRegistration: organization.isOpenRegistration,
          createdAt: organization.createdAt,
        },
      })
    } catch (error) {
      if (error instanceof OrganizationError) {
        return Response.json({ error: error.message }, { status: 400 })
      }
      console.error('Error creating organization:', error)
      return Response.json({ error: 'An unexpected error occurred' }, { status: 500 })
    }
  }

  // Update organization's problem set
  if (action === 'update') {
    const id = formData.get('id')?.toString()
    const problemSetId = formData.get('problemSetId')?.toString()

    if (!id || !problemSetId) {
      return Response.json(
        { error: 'Organization ID and problem set ID are required' },
        { status: 400 },
      )
    }

    try {
      await updateOrganizationProblemSet(id, problemSetId)
      return Response.json({
        success: true,
        message: 'Organization updated successfully',
      })
    } catch (error) {
      if (error instanceof OrganizationError) {
        return Response.json({ error: error.message }, { status: 400 })
      }
      console.error('Error updating organization:', error)
      return Response.json({ error: 'An unexpected error occurred' }, { status: 500 })
    }
  }

  // Delete an organization
  if (action === 'delete') {
    const id = formData.get('id')?.toString()

    if (!id) {
      return Response.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    try {
      await deleteOrganization(id)
      return Response.json({
        success: true,
        message: 'Organization deleted successfully',
      })
    } catch (error) {
      if (error instanceof OrganizationError) {
        return Response.json({ error: error.message }, { status: 400 })
      }
      console.error('Error deleting organization:', error)
      return Response.json({ error: 'An unexpected error occurred' }, { status: 500 })
    }
  }

  return Response.json({ error: 'Invalid action' }, { status: 400 })
}

export default function OrganizationsAdmin() {
  const { organizations, problemSets } = useLoaderData<typeof loader>()
  const actionData = useActionData<typeof action>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === 'submitting'

  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingOrgId, setEditingOrgId] = useState<string | null>(null)

  return (
    <div className="container mx-auto">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Organizations</h1>
        <Button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Organization
        </Button>
      </div>

      {showCreateForm && (
        <CreateOrganizationForm
          problemSets={problemSets}
          isSubmitting={isSubmitting}
          onCancel={() => setShowCreateForm(false)}
        />
      )}

      <ActionMessage
        success={actionData?.success}
        error={actionData?.error}
        message={actionData?.message}
      />

      <OrganizationTable
        organizations={organizations}
        problemSets={problemSets}
        isSubmitting={isSubmitting}
        editingOrgId={editingOrgId}
        onStartEdit={(orgId) => setEditingOrgId(orgId)}
        onCancelEdit={() => setEditingOrgId(null)}
      />
    </div>
  )
}
