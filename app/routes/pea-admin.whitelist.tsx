import React, { useState } from "react";
import {
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
  redirect,
} from "@remix-run/node";
import { Form, useActionData, useLoaderData } from "@remix-run/react";
import { Button } from "~/components/ui/button";
import { Loader2, Plus, RotateCcw, Trash, Upload } from "lucide-react";
import {
  addEmailToWhitelist,
  EmailVerificationError,
  getWhitelist,
  importWhitelist,
  removeEmailFromWhitelist,
} from "~/lib/email.server";
import { deleteUserTests, getUserTests } from "~/lib/test.server";
import { User } from "~/models";
import { validateEmail } from "~/lib/utils";
import { useSubmitting } from "~/components/hooks/use-submitting";
import { requireAdminAccess } from "~/lib/session.server";

interface EmailWithStatus {
  email: string;
  tests?: Array<{
    id: string;
    createdAt?: Date;
  }>;
}

export async function loader({ request }: LoaderFunctionArgs) {
  // Check if user has admin access
  const isAdmin = await requireAdminAccess(request);
  if (!isAdmin) {
    return redirect("/pea-admin");
  }

  try {
    const whitelist = await getWhitelist();
    if (!Array.isArray(whitelist)) {
      console.error("Whitelist is not an array:", whitelist);
      return Response.json({
        whitelist: [],
        error: "Failed to load whitelist: Invalid data format",
      });
    }

    const emailsWithStatus = await Promise.all(
      whitelist.filter(Boolean).map(async (email) => {
        try {
          const user = await User.findOne({
            where: { email },
          });

          const result: EmailWithStatus = { email };

          if (user) {
            const tests = await getUserTests(user.id);
            if (tests && tests.length > 0) {
              result.tests = tests.map((test) => ({
                id: test.id,
                createdAt: test.createdAt,
              }));
            }
          }

          return result;
        } catch (error) {
          console.error(`Error checking tests for ${email}:`, error);
          return { email };
        }
      }),
    );

    return Response.json({
      whitelist: emailsWithStatus,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return Response.json({
      whitelist: [],
      error: "Failed to load whitelist: " +
        (error instanceof Error ? error.message : String(error)),
    });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  // Check if user has admin access
  const isAdmin = await requireAdminAccess(request);
  if (!isAdmin) {
    return redirect("/pea-admin");
  }

  const formData = await request.formData();
  const action = formData.get("action")?.toString();

  if (action === "add-email") {
    const email = formData.get("email")?.toString();
    if (!email || !validateEmail(email)) {
      return Response.json(
        { error: "Invalid email format" },
        {
          status: 400,
        },
      );
    }

    try {
      await addEmailToWhitelist(email);
      const result = await loader({ request } as LoaderFunctionArgs);
      const { whitelist } = await result.json();
      return Response.json({
        success: true,
        message: `Email ${email} added to whitelist`,
        whitelist,
      });
    } catch (error) {
      if (error instanceof EmailVerificationError) {
        return Response.json({ error: error.message }, { status: 400 });
      }
      return Response.json(
        { error: "Failed to add email to whitelist" },
        { status: 500 },
      );
    }
  }

  if (action === "remove-email") {
    const email = formData.get("email")?.toString();

    if (!email) {
      return Response.json(
        { error: "Email is required" },
        {
          status: 400,
        },
      );
    }

    try {
      const removed = await removeEmailFromWhitelist(email);
      const result = await loader({ request } as LoaderFunctionArgs);
      const { whitelist } = await result.json();

      if (!removed) {
        return Response.json({
          warning: `Email ${email} was not in the whitelist`,
          whitelist,
        });
      }

      return Response.json({
        success: true,
        message: `Email ${email} removed from whitelist`,
        whitelist,
      });
    } catch (error) {
      return Response.json(
        {
          error: "Failed to remove email from whitelist",
        },
        { status: 500 },
      );
    }
  }

  if (action === "reset-tests") {
    const email = formData.get("email")?.toString();

    if (!email) {
      return Response.json(
        { error: "Email is required" },
        {
          status: 400,
        },
      );
    }

    try {
      const deletedCount = await deleteUserTests(email);
      const result = await loader({ request } as LoaderFunctionArgs);
      const { whitelist } = await result.json();

      if (deletedCount === 0) {
        return Response.json({
          warning: `No tests found for ${email}`,
          whitelist,
        });
      }

      return Response.json({
        success: true,
        message: `Successfully reset ${deletedCount} test(s) for ${email}`,
        whitelist,
      });
    } catch (error) {
      return Response.json(
        {
          error: "Failed to reset tests",
        },
        { status: 500 },
      );
    }
  }

  if (action === "import-emails") {
    const emailsText = formData.get("emails")?.toString();

    if (!emailsText) {
      return Response.json(
        { error: "No emails provided" },
        {
          status: 400,
        },
      );
    }

    try {
      const emails = emailsText
        .split(/[\n,;]/)
        .map((email) => email.trim())
        .filter((email) => email.length > 0);

      if (emails.length === 0) {
        return Response.json(
          { error: "No valid emails found" },
          {
            status: 400,
          },
        );
      }

      const result = await importWhitelist(emails);
      const loaderResult = await loader({ request } as LoaderFunctionArgs);
      const { whitelist } = await loaderResult.json();

      return Response.json({
        success: true,
        message:
          `Successfully added ${result.added.length} emails to whitelist`,
        failed: result.failed.length > 0 ? result.failed : undefined,
        whitelist,
      });
    } catch (error) {
      return Response.json(
        { error: "Failed to import emails" },
        {
          status: 500,
        },
      );
    }
  }

  return Response.json({ error: "Invalid action" }, { status: 400 });
}

export default function WhitelistAdmin() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const isSubmitting = useSubmitting();
  const [importMode, setImportMode] = useState(false);

  const [emails, setEmails] = useState<EmailWithStatus[]>(() => {
    return Array.isArray(loaderData.whitelist)
      ? loaderData.whitelist.filter(
        (item: EmailWithStatus) => item && item.email,
      )
      : [];
  });

  React.useEffect(() => {
    if (actionData?.whitelist && Array.isArray(actionData.whitelist)) {
      const validEmails = actionData.whitelist.filter(
        (item: EmailWithStatus) => item && item.email,
      );
      setEmails(validEmails);
    }
  }, [actionData]);

  return (
    <div className="h-full mx-auto shadow-sm">
      <div className="p-4 sm:p-8 bg-white">
        <div className="space-y-4 sm:space-y-8">
          <div>
            <h1 className="text-2xl font-medium text-gray-900">
              Whitelist Management
            </h1>
            <p className="mt-2 text-sm text-gray-500">
              Only emails in this whitelist will be able to register and login
              to the platform.
            </p>
          </div>

          <div className="border-t border-gray-200 pt-6">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              {!importMode
                ? (
                  <Form
                    method="post"
                    className="flex-1 flex gap-2"
                    key="add-form"
                  >
                    <input type="hidden" name="action" value="add-email" />
                    <div className="flex-1">
                      <input
                        type="email"
                        name="email"
                        placeholder="Add email to whitelist"
                        required
                        className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                      />
                    </div>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting
                        ? <Loader2 className="w-4 h-4 animate-spin" />
                        : <Plus className="w-4 h-4" />}
                      <span className="ml-1">Add</span>
                    </Button>
                  </Form>
                )
                : (
                  <Form method="post" className="flex-1" key="import-form">
                    <input
                      type="hidden"
                      name="action"
                      value="import-emails"
                    />
                    <textarea
                      name="emails"
                      placeholder="Enter emails (one per line or comma/semicolon separated)"
                      required
                      rows={5}
                      className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm mb-2"
                    />
                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setImportMode(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting
                          ? <Loader2 className="w-4 h-4 animate-spin mr-1" />
                          : <Upload className="w-4 h-4 mr-1" />}
                        Import
                      </Button>
                    </div>
                  </Form>
                )}

              {!importMode && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setImportMode(true)}
                  className="whitespace-nowrap"
                >
                  <Upload className="w-4 h-4 mr-1" />
                  Batch Import
                </Button>
              )}
            </div>

            {/* Whitelist Results */}
            <div>
              <div className="flex items-center justify-between mb-3">
                {emails.length > 0 && (
                  <span className="text-sm text-gray-500">
                    {emails.length} {emails.length === 1 ? "email" : "emails"}
                  </span>
                )}
              </div>

              {emails.length === 0
                ? (
                  <div className="bg-gray-50 rounded-lg p-6 text-center text-gray-500">
                    No emails in the whitelist yet. Add some emails to get
                    started.
                  </div>
                )
                : (
                  <div className="divide-y divide-gray-200 rounded-lg border border-gray-200">
                    {emails.map((item) => (
                      <div
                        key={item.email}
                        className="flex items-center justify-between p-3 hover:bg-gray-50"
                      >
                        <div className="text-md text-gray-800 flex-1">
                          <div>{item.email}</div>

                          {item.tests && item.tests.length > 0 && (
                            <div className="mt-1">
                              <div className="text-sm text-gray-500">
                                Test ID: {item.tests[0].id}
                              </div>
                              <div className="text-sm text-gray-500">
                                Created: {new Date(item.tests[0].createdAt ?? "").toLocaleString()}
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center">
                          {item.tests && item.tests.length > 0 && (
                            <Form method="post" className="mr-2">
                              <input
                                type="hidden"
                                name="action"
                                value="reset-tests"
                              />
                              <input
                                type="hidden"
                                name="email"
                                value={item.email}
                              />
                              <Button
                                type="submit"
                                variant="outline"
                                size="sm"
                                className="text-amber-500 hover:text-amber-700 hover:bg-amber-50"
                                title="Reset Tests"
                              >
                                <RotateCcw className="w-4 h-4" />
                              </Button>
                            </Form>
                          )}
                          <Form method="post">
                            <input
                              type="hidden"
                              name="action"
                              value="remove-email"
                            />
                            <input
                              type="hidden"
                              name="email"
                              value={item.email}
                            />
                            <Button
                              type="submit"
                              variant="ghost"
                              size="sm"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              title="Remove Email"
                            >
                              <Trash className="w-4 h-4" />
                            </Button>
                          </Form>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
            </div>
          </div>

          {/* Messages */}
          {loaderData.error && (
            <div className="flex items-center gap-2 text-red-600 text-sm p-3 bg-red-50 rounded-lg">
              <div className="w-1 h-4 bg-red-600 rounded-full" />
              <p>{loaderData.error}</p>
            </div>
          )}

          {actionData?.error && (
            <div className="flex items-center gap-2 text-red-600 text-sm p-3 bg-red-50 rounded-lg">
              <div className="w-1 h-4 bg-red-600 rounded-full" />
              <p>{actionData.error}</p>
            </div>
          )}

          {actionData?.warning && (
            <div className="flex items-center gap-2 text-amber-600 text-sm p-3 bg-amber-50 rounded-lg">
              <div className="w-1 h-4 bg-amber-600 rounded-full" />
              <p>{actionData.warning}</p>
            </div>
          )}

          {actionData?.success && (
            <div className="flex items-center gap-2 text-green-600 text-sm p-3 bg-green-50 rounded-lg">
              <div className="w-1 h-4 bg-green-600 rounded-full" />
              <p>{actionData.message}</p>
              {actionData.failed && actionData.failed.length > 0 && (
                <p className="mt-1">
                  Failed to import {actionData.failed.length} invalid emails.
                </p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
