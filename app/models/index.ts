import { Sequelize } from 'sequelize'
import { UserModel } from './user.model'
import { ProblemSetModel } from './problem-set.model'
import { TestModel } from './test.model'
import { WhitelistModel } from './whitelist.model'
import { VerificationCodeModel } from './verification-code.model'
import { OrganizationModel } from './organization.model'
import { OrganizationUserModel } from './organization-user.model'

const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: process.env.DATABASE_URL || './data/sqlite.db',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
})

// Initialize models
export const User = UserModel(sequelize)
export const ProblemSet = ProblemSetModel(sequelize)
export const Test = TestModel(sequelize)
export const Whitelist = WhitelistModel(sequelize)
export const VerificationCode = VerificationCodeModel(sequelize)
export const Organization = OrganizationModel(sequelize)
export const OrganizationUser = OrganizationUserModel(sequelize)

// Define associations
ProblemSet.hasMany(Test, { foreignKey: 'problemSetId', as: 'tests' })
Test.belongsTo(ProblemSet, { foreignKey: 'problemSetId' })

User.hasMany(Test, { foreignKey: 'userId', as: 'tests' })
Test.belongsTo(User, { foreignKey: 'userId' })

Organization.belongsTo(ProblemSet, { foreignKey: 'problemSetId' })
ProblemSet.hasMany(Organization, { foreignKey: 'problemSetId', as: 'organizations' })

// Organization-User associations
Organization.hasMany(OrganizationUser, { foreignKey: 'organizationId', as: 'organizationUsers' })
OrganizationUser.belongsTo(Organization, { foreignKey: 'organizationId' })

User.hasMany(OrganizationUser, { foreignKey: 'userId', as: 'organizationUsers' })
OrganizationUser.belongsTo(User, { foreignKey: 'userId' })

sequelize
  .sync()
  .then(() => {
    console.log('Database synced successfully')
  })
  .catch((error: Error) => {
    console.error('Failed to sync database:', error)
  })

export { sequelize }
