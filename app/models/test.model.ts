import { DataTypes, Model, Sequelize, InferAttributes, InferCreationAttributes } from 'sequelize'
import type { Test } from '~/lib/types'

export class TestInstance extends Model<
  InferAttributes<TestInstance>,
  InferCreationAttributes<TestInstance>
> implements Test {
  declare id: string
  declare problemSetId: string
  declare userId: string
  declare url: string
  declare createdAt?: Date

  static initialize(sequelize: Sequelize) {
    return this.init(
      {
        id: {
          type: DataTypes.STRING,
          primaryKey: true,
        },
        problemSetId: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'problem_set_id',
        },
        userId: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'user_id',
        },
        url: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        createdAt: {
          type: DataTypes.DATE,
          field: 'created_at',
          defaultValue: DataTypes.NOW,
        },
      },
      {
        sequelize,
        tableName: 'tests',
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: false,
      },
    )
  }
}

export function TestModel(sequelize: Sequelize) {
  return TestInstance.initialize(sequelize)
} 