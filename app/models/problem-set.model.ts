import { DataTypes, Model, Sequelize, InferAttributes, InferCreationAttributes } from 'sequelize'
import type { ProblemSet } from '~/lib/types'

export class ProblemSetInstance
  extends Model<InferAttributes<ProblemSetInstance>, InferCreationAttributes<ProblemSetInstance>>
  implements ProblemSet
{
  declare id: string
  declare problemIds: string[]
  declare createdAt?: Date

  static initialize(sequelize: Sequelize) {
    return this.init(
      {
        id: {
          type: DataTypes.STRING,
          primaryKey: true,
        },
        problemIds: {
          type: DataTypes.TEXT,
          allowNull: false,
          field: 'problem_ids',
          get(this: ProblemSetInstance): string[] {
            const jsonString = this.getDataValue('problemIds') as unknown as string
            return jsonString ? JSON.parse(jsonString) : []
          },
          set(this: ProblemSetInstance, value: string[]) {
            this.setDataValue('problemIds', JSON.stringify(value) as unknown as string[])
          },
        },
        createdAt: {
          type: DataTypes.DATE,
          field: 'created_at',
        },
      },
      {
        sequelize,
        tableName: 'problem_sets',
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: false,
      },
    )
  }
}

export function ProblemSetModel(sequelize: Sequelize) {
  return ProblemSetInstance.initialize(sequelize)
}
