import { DataTypes, Model, Sequelize } from 'sequelize'

interface VerificationCodeAttributes {
  id: string
  email: string
  code: string
  expiresAt: Date
  createdAt?: Date
  updatedAt?: Date
}

export class VerificationCode extends Model<VerificationCodeAttributes> implements VerificationCodeAttributes {
  public id!: string
  public email!: string
  public code!: string
  public expiresAt!: Date
  public readonly createdAt!: Date
  public readonly updatedAt!: Date
}

export function VerificationCodeModel(sequelize: Sequelize) {
  return VerificationCode.init(
    {
      id: {
        type: DataTypes.STRING,
        primaryKey: true,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isEmail: true,
          notEmpty: true,
        },
      },
      code: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      tableName: 'verification_codes',
      timestamps: true,
      indexes: [
        {
          fields: ['email'],
        },
        {
          fields: ['expiresAt'],
        },
      ],
    }
  )
} 