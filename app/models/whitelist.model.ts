import { DataTypes, Model, Sequelize } from 'sequelize'

interface WhitelistAttributes {
  id: string
  email: string
  createdAt?: Date
  updatedAt?: Date
}

export class Whitelist extends Model<WhitelistAttributes> implements WhitelistAttributes {
  public id!: string
  public email!: string
  public readonly createdAt!: Date
  public readonly updatedAt!: Date
}

export function WhitelistModel(sequelize: Sequelize) {
  return Whitelist.init(
    {
      id: {
        type: DataTypes.STRING,
        primaryKey: true,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true,
          notEmpty: true,
        },
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      tableName: 'whitelist',
      timestamps: true,
      indexes: [
        {
          unique: true,
          fields: ['email'],
        },
      ],
    }
  )
} 