import { DataTypes, Model, Sequelize, InferAttributes, InferCreationAttributes } from 'sequelize'
import type { User } from '~/lib/types'

export class UserInstance extends Model<
  InferAttributes<UserInstance>,
  InferCreationAttributes<UserInstance>
> implements User {
  declare id: string
  declare name: string
  declare email?: string
  declare phoneNumber?: string
  declare createdAt?: Date

  static initialize(sequelize: Sequelize) {
    return this.init(
      {
        id: {
          type: DataTypes.STRING,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        email: {
          type: DataTypes.STRING,
          unique: true,
        },
        phoneNumber: {
          type: DataTypes.STRING,
          unique: true,
          field: 'phone_number',
        },
        createdAt: {
          type: DataTypes.DATE,
          field: 'created_at',
          defaultValue: DataTypes.NOW,
        },
      },
      {
        sequelize,
        tableName: 'users',
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: false,
      },
    )
  }
}

export function UserModel(sequelize: Sequelize) {
  return UserInstance.initialize(sequelize)
} 