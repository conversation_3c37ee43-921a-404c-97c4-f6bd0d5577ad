import { Check, Copy } from 'lucide-react'
import { useEffect, useState } from 'react'
import { cn } from '~/lib/utils'

interface CopyButtonProps {
  text: string
  className?: string
}

export function CopyButton({ text, className }: CopyButtonProps) {
  const [isCopy, setIsCopy] = useState(false)

  useEffect(() => {
    let timeout: NodeJS.Timeout

    if (isCopy) {
      timeout = setTimeout(() => setIsCopy(false), 2000)
    }

    return () => {
      if (timeout) {
        clearTimeout(timeout)
      }
    }
  }, [isCopy])

  const handleCopy = async () => {
    await navigator.clipboard.writeText(text)
    setIsCopy(true)
  }

  return (
    <button onClick={handleCopy} className="text-gray-400 hover:text-gray-800 cursor-pointer">
      {isCopy ? (
        <Check className={cn('w-4 h-4', className)} />
      ) : (
        <Copy className={cn('w-4 h-4', className)} />
      )}
    </button>
  )
}
