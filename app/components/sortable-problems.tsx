import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { useState, useEffect } from 'react'

interface SortableItemProps {
  id: string
}

function SortableItem({ id }: SortableItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm mb-2 cursor-move hover:bg-gray-50 transition"
    >
      <div className="flex items-center text-gray-900">
        <svg
          className="w-5 h-5 mr-2 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
        </svg>
        <span className="font-medium">{id}</span>
      </div>
    </div>
  )
}

interface SortableProblemsProps {
  problems: string[]
  onChange: (problems: string[]) => void
}

export function SortableProblems({ problems, onChange }: SortableProblemsProps) {
  const [items, setItems] = useState<string[]>([])

  // Update items when problems prop changes
  useEffect(() => {
    setItems(problems)
  }, [problems])

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={({ active, over }) => {
        if (over && active.id !== over.id) {
          const oldIndex = items.indexOf(active.id.toString())
          const newIndex = items.indexOf(over.id.toString())
          const newItems = arrayMove(items, oldIndex, newIndex)
          setItems(newItems)
          onChange(newItems)
        }
      }}
    >
      <SortableContext items={items} strategy={verticalListSortingStrategy}>
        {items.map((id) => (
          <SortableItem key={id} id={id} />
        ))}
      </SortableContext>
    </DndContext>
  )
}
